<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>奢侈耳环争议事件 - 黄杨钿甜成人礼风波</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 100vw;
            height: 100vh;
            background-color: #FFFFFF;
            font-family: '微软雅黑', 'SimHei', Arial, sans-serif;
            overflow: hidden;
            position: relative;
        }

        .container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        /* 图片元素样式 */
        .image-element {
            position: absolute;
            left: 50%;
            top: 50%;
            transform-origin: center center;
            opacity: 0;
        }

        /* 文字元素样式 */
        .text-element {
            position: absolute;
            left: 50%;
            top: 50%;
            transform-origin: center center;
            opacity: 0;
            white-space: nowrap;
        }

        /* 具体元素定位和样式 */
        #char_hydt {
            --x-offset: 0vw;
            --y-offset: 0vh;
            width: 32.14vw;
            height: 42.85vh;
            transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
            object-fit: cover;
        }

        #char_hydt_closeup {
            --x-offset: 0vw;
            --y-offset: 0vh;
            width: 50vw;
            height: 50vh;
            transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
            object-fit: contain;
        }

        #prop_earring {
            --x-offset: 0vw;
            --y-offset: 0vh;
            width: 42.85vw;
            height: 42.85vh;
            transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
            object-fit: contain;
        }

        #prop_income_chart {
            --x-offset: 0vw;
            --y-offset: -15.625vh;
            width: 57.14vw;
            height: 32.14vh;
            transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
            object-fit: contain;
        }

        #text_title {
            --x-offset: 0vw;
            --y-offset: 43.75vh;
            font-size: 24px;
            color: #00008B;
            font-weight: bold;
            text-align: center;
            transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
        }

        #text_tag {
            --x-offset: 42.857vw;
            --y-offset: 43.75vh;
            font-size: 16px;
            color: #FFFFFF;
            background-color: #FF0000;
            padding: 3px 8px;
            border-radius: 4px;
            text-align: center;
            transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
        }

        #text_date {
            --x-offset: -35.714vw;
            --y-offset: -37.5vh;
            font-size: 18px;
            color: #000000;
            text-align: left;
            transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
        }

        #text_brand {
            --x-offset: 0vw;
            --y-offset: -18.75vh;
            font-size: 20px;
            color: #00008B;
            font-weight: bold;
            text-align: center;
            transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
        }

        #text_price {
            --x-offset: 0vw;
            --y-offset: -18.75vh;
            font-size: 28px;
            color: #FF0000;
            font-weight: bold;
            text-align: center;
            transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
        }

        #text_comparison {
            --x-offset: 0vw;
            --y-offset: 6.25vh;
            font-size: 18px;
            color: #000000;
            font-weight: bold;
            text-align: center;
            transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
        }

        #text_conclusion {
            --x-offset: 0vw;
            --y-offset: -43.75vh;
            font-size: 22px;
            color: #8B0000;
            font-weight: bold;
            text-align: center;
            transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
        }

        /* 最终场景布局 */
        .final-layout #char_hydt {
            --x-offset: -21.428vw;
            --y-offset: 25vh;
            width: 21.43vw;
            height: 28.57vh;
        }

        .final-layout #prop_earring {
            --x-offset: 21.428vw;
            --y-offset: 25vh;
            width: 21.43vw;
            height: 21.43vh;
        }

        /* 动画定义 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        @keyframes slideInFromTop {
            from { 
                opacity: 0;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset) - 50px));
            }
            to { 
                opacity: 1;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
            }
        }

        @keyframes zoomToEar {
            from { 
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) scale(1);
            }
            to { 
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) scale(1.5) translateX(-10%) translateY(-10%);
            }
        }

        @keyframes magnifyIn {
            from { 
                opacity: 0;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) scale(0.8);
            }
            to { 
                opacity: 1;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) scale(1);
            }
        }

        @keyframes slideOutLeft {
            from { 
                opacity: 1;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
            }
            to { 
                opacity: 0;
                transform: translate(calc(-50% + var(--x-offset) - 100vw), calc(-50% + var(--y-offset)));
            }
        }

        @keyframes slideInLeft {
            from { 
                opacity: 0;
                transform: translate(calc(-50% + var(--x-offset) - 100vw), calc(-50% + var(--y-offset)));
            }
            to { 
                opacity: 1;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
            }
        }

        @keyframes slideInRight {
            from { 
                opacity: 0;
                transform: translate(calc(-50% + var(--x-offset) + 100vw), calc(-50% + var(--y-offset)));
            }
            to { 
                opacity: 1;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
            }
        }

        @keyframes fadeInUp {
            from { 
                opacity: 0;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset) + 50px));
            }
            to { 
                opacity: 1;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));
            }
        }

        @keyframes priceScale {
            from { 
                opacity: 0;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) scale(0.5);
            }
            to { 
                opacity: 1;
                transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset))) scale(1);
            }
        }
    </style>
</head>
<body>
    <div class="container" id="container">
        <!-- 人物图片 -->
        <img id="char_hydt" class="image-element" src="../data/assets/characters/yhdt.jpg" alt="黄杨钿甜">
        <img id="char_hydt_closeup" class="image-element" src="../data/assets/props/yhdt_earring.jpg" alt="黄杨钿甜耳环佩戴特写">
        
        <!-- 道具图片 -->
        <img id="prop_earring" class="image-element" src="../data/assets/props/yhdt_earring.jpg" alt="GRAFF耳环">
        <img id="prop_income_chart" class="image-element" src="../data/assets/props/income_chart.png" alt="公务员收入图表">
        
        <!-- 文字元素 -->
        <div id="text_title" class="text-element">奢侈耳环</div>
        <div id="text_tag" class="text-element">争议事件</div>
        <div id="text_date" class="text-element">2025年5月</div>
        <div id="text_brand" class="text-element">GRAFF奢侈珠宝</div>
        <div id="text_price" class="text-element">¥2,300,000</div>
        <div id="text_comparison" class="text-element">公务员收入 vs 耳环价值</div>
        <div id="text_conclusion" class="text-element">经济条件严重不符</div>
    </div>

    <script>
        // 预加载所有图片
        function preloadImages() {
            const imageUrls = [
                '黄杨钿甜.jpg',
                '黄杨钿甜耳环佩戴局部特写.jpg',
                '黄杨钿甜佩戴的graff耳环.jpg',
                '公务员收入数据图表.png'
            ];
            
            const promises = imageUrls.map(url => {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    img.onload = resolve;
                    img.onerror = reject;
                    img.src = url;
                });
            });
            
            return Promise.all(promises);
        }

        // 打字机效果函数
        function typewriterEffect(elementId, text, duration) {
            const element = document.getElementById(elementId);
            element.style.opacity = '1';
            element.textContent = '';
            
            const chars = text.split('');
            const delay = duration * 1000 / chars.length;
            
            chars.forEach((char, index) => {
                setTimeout(() => {
                    element.textContent += char;
                }, index * delay);
            });
        }

        // 价格动画函数
        function animatePrice() {
            const element = document.getElementById('text_price');
            element.style.opacity = '1';
            element.style.animation = 'priceScale 0.5s ease-out forwards';
            
            let currentValue = 0;
            const targetValue = 2300000;
            const duration = 1000; // 1秒
            const increment = targetValue / (duration / 50); // 每50ms更新一次
            
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= targetValue) {
                    currentValue = targetValue;
                    clearInterval(timer);
                }
                element.textContent = `¥${Math.floor(currentValue).toLocaleString()}`;
            }, 50);
        }

        // 核心动画调度器
        function animateScene() {
            // AUDIO CUE: 开场音效 - 事件介绍开始
            
            // 0s-3s: 第一场景 - 人物介绍
            setTimeout(() => {
                document.getElementById('char_hydt').style.animation = 'fadeIn 0.5s ease-in forwards';
            }, 0);

            setTimeout(() => {
                document.getElementById('text_title').style.animation = 'slideInFromTop 0.7s ease-out forwards';
            }, 200);

            setTimeout(() => {
                document.getElementById('text_tag').style.animation = 'fadeIn 0.5s ease-in forwards';
            }, 500);

            setTimeout(() => {
                document.getElementById('text_date').style.animation = 'fadeIn 0.5s ease-in forwards';
            }, 700);

            setTimeout(() => {
                document.getElementById('char_hydt').style.animation = 'zoomToEar 1s ease-in-out forwards';
            }, 2500);

            // 3s-6s: 第二场景 - 耳环特写
            setTimeout(() => {
                // AUDIO CUE: 放大镜音效 - 耳环特写显示
                document.getElementById('char_hydt').style.animation = 'fadeOut 0.3s ease-out forwards';
                document.getElementById('text_date').style.animation = 'fadeOut 0.3s ease-out forwards';
            }, 3000);

            setTimeout(() => {
                document.getElementById('char_hydt_closeup').style.animation = 'magnifyIn 0.5s ease-out forwards';
            }, 3100);

            setTimeout(() => {
                typewriterEffect('text_brand', 'GRAFF奢侈珠宝', 0.8);
            }, 3800);

            // 6s-9s: 第三场景 - 耳环产品和价格
            setTimeout(() => {
                document.getElementById('char_hydt_closeup').style.animation = 'fadeOut 0.3s ease-out forwards';
                document.getElementById('text_brand').style.animation = 'fadeOut 0.3s ease-out forwards';
            }, 6000);

            setTimeout(() => {
                document.getElementById('prop_earring').style.animation = 'fadeIn 0.5s ease-in forwards';
            }, 6100);

            setTimeout(() => {
                // AUDIO CUE: 计数器音效 - 价格显示
                animatePrice();
            }, 6300);

            // 9s-12s: 第四场景 - 最终对比
            setTimeout(() => {
                document.getElementById('prop_earring').style.animation = 'slideOutLeft 0.5s ease-in forwards';
                document.getElementById('text_price').style.animation = 'fadeOut 0.3s ease-out forwards';
            }, 9000);

            setTimeout(() => {
                document.getElementById('container').classList.add('final-layout');
                document.getElementById('char_hydt').style.animation = 'slideInLeft 0.5s ease-out forwards';
                document.getElementById('prop_earring').style.animation = 'slideInRight 0.5s ease-out forwards';
            }, 9100);

            setTimeout(() => {
                // AUDIO CUE: 笔记本翻页声 - 收入图表显示
                document.getElementById('prop_income_chart').style.animation = 'fadeInUp 0.5s ease-out forwards';
            }, 9200);

            setTimeout(() => {
                document.getElementById('text_comparison').style.animation = 'fadeIn 0.4s ease-in forwards';
            }, 9400);

            setTimeout(() => {
                typewriterEffect('text_conclusion', '经济条件严重不符', 1);
            }, 9800);
        }

        // 启动动画
        window.addEventListener('load', () => {
            preloadImages().then(() => {
                animateScene();
            }).catch(error => {
                console.warn('部分图片加载失败，继续播放动画:', error);
                animateScene();
            });
        });

        // 点击重新播放
        document.addEventListener('click', () => {
            location.reload();
        });
    </script>
</body>
</html>