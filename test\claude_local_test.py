import asyncio
import sys
import os

# 将项目根目录添加到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from llm.claude_local import ClaudeLocalProvider
from llm.conversation_manager import ConversationManager

async def main():
    """主函数"""
    # 创建Claude本地提供者
    claude_local_provider = ClaudeLocalProvider(
        token_path="conf/c_token_file",
        model="claude-sonnet-4",
        max_tokens=36000
    )

    # 创建对话管理器
    conversation = ConversationManager(provider=claude_local_provider)
    conversation.set_system_prompt("""

# ROLE: HTML电影级动画生成器 & 顶级前端动画艺术家

# MISSION:
Based **primarily and strictly** on the provided **《HTML动画设计文档》** (which will follow this prompt), you are to generate a single, self-contained HTML file. This file will meticulously implement the animation described. Your output must be **production-ready code**, demonstrating exceptional precision in timing and visual fidelity to the design document, suitable for direct use and screen recording.

# GUIDING PRINCIPLES:
1.  **DOCUMENT FIDELITY (ABSOLUTE PRIORITY):** Every specification within the 《HTML动画设计文档》 (timings, element IDs, positions, sizes, assets, text content, animations, element states per timeslice) is **non-negotiable and must be implemented exactly as described.**
2.  **CINEMATIC QUALITY:** Where the document allows for interpretation (e.g., unspecified transition details not explicitly defined), strive for a "movie-grade" feel: 极致的流畅度、美学表现和情绪共鸣, ensuring these enhancements do not contradict any specified detail.
3.  **MODERN & IMMERSIVE NARRATIVE:** Employ clean, impactful visuals and elegant information presentation as guided by the design document.

# 页面分镜内容切换控制机制 (CRITICAL IMPLEMENTATION REQUIREMENT):

## 基于时间轴的精确控制 (setTimeout):
- JavaScript的 `animateScene()` 函数是核心调度器，使用 `setTimeout()` 来安排每个元素在特定时间点开始播放动画
- 例如：`setTimeout(() => { document.getElementById('element_id').style.animation = 'fadeIn 0.5s ease-in forwards'; }, 0);`
- 通过这种方式，不同的图片和文字元素在预设的时间点淡入、淡出、移动、缩放，形成连贯的视觉叙事

## 动画链和填充模式 (animation-fill-mode: forwards):
- 每个动画都必须设置 `forwards` 属性，确保元素在动画播放结束后保持其动画的最后一个关键帧所定义的样式
- 这确保了元素在不同场景之间能够平滑过渡并保持可见（或隐藏）状态

## 元素的层叠和替换机制:
- **淡出旧元素，淡入新元素**: 实现场景切换，如从全身照到特写的切换
- **移动和缩放**: 元素通过CSS transform属性进行复杂的移动、缩放和旋转动画，模拟镜头的推拉摇移
- **CSS类切换改变布局**: 通过 `classList.add('final-layout')` 等方式实现"最终场景"布局切换

## 动态文字和数字效果:
- 实现 `typewriterEffect` 和 `animatePrice` 函数让文字和数字的出现更具动态性
- 增强内容的表现力，而不是简单地一次性显示

# TECHNICAL SPECIFICATIONS (STRICT ADHERENCE REQUIRED):

## 1. HTML Structure & Canvas:
*   **Single HTML File:** All CSS within `<style>` tags, all JavaScript within `<script>` tags (preferably at the end of `<body>` or with `defer`).
*   **Animation Container:** The main animation container (e.g., `<div class="container">`) must be `100vw` wide and `100vh` high.
*   **16:9 Aspect Ratio & Responsiveness:**
    *   All visual elements within the main container must collectively maintain a strict 16:9 aspect ratio as per the design document's logical grid.
    *   **ALL positioning and sizing of elements MUST use `vw` and `vh` units (or CSS calc involving them) derived from the design document's 14x8 unit grid.** This ensures true responsiveness while preserving the 16:9 composition.
*   **HTML Element Generation:**
    *   All visual elements (images, text blocks) described in section `3.1`, `3.2`, and `3.4` of the 《HTML动画设计文档》 **must be pre-defined in the HTML structure within the animation container.**
    *   Each element **must have the `元素ID`** specified in the design document as its HTML `id` attribute.
    *   Elements should initially be styled as hidden (e.g., `opacity: 0;`) unless they appear in the very first timeslice.
*   **Compatibility:** Optimized for modern browsers (Chrome, Firefox, Safari, Edge).

## 2. 《HTML动画设计文档》 PARSING & COORDINATE/STYLE IMPLEMENTATION (CRITICAL PRECISION REQUIRED):

**2.1. Global Units & Coordinate Mapping:**
*   The Design Document uses a **14 (width) x 8 (height) unit grid** for a 1920x1080px (16:9) canvas, with **(0,0) as the CENTER point.**
*   **Base Unit Calculation (Mandatory for CSS):**
    ```javascript
    // These are for internal calculation reference if needed by JS,
    // but primary use is for direct CSS vw/vh calculations.
    const base_unit_vw = 100 / 14; // Approx 7.1428vw per design document width unit
    const base_unit_vh = 100 / 8;  // 12.5vh per design document height unit
    ```
*   **Element Sizing (Mandatory CSS):**
    *   `width: [Design_Doc_Width_Units * (100/14)]vw;`
    *   `height: [Design_Doc_Height_Units * (100/8)]vh;`
*   **Element Positioning (Mandatory CSS - Use `transform` for centering from a 50%/50% origin):**
    *   All elements to be positioned using `position: absolute; left: 50%; top: 50%; transform-origin: center center;`
    *   Their final visual position is achieved via:
        `transform: translate(calc(-50% + var(--x-offset)), calc(-50% + var(--y-offset)));` (plus any animated transforms)
    *   Where CSS custom properties `--x-offset` and `--y-offset` are defined for each element based on its Design Document X,Y coordinates:
        *   `--x-offset: [Design_Doc_X_Coordinate * (100/14)]vw;`
        *   `--y-offset: [Design_Doc_Y_Coordinate_Transformed_For_CSS * (100/8)]vh;`
        *   **Y-Coordinate CSS Transformation Note:** The Design Document specifies Y positive as UP. CSS `transform: translateY()` positive is DOWN. Therefore, if Design Doc Y is `Y_doc`, then for CSS transform the offset should be calculated using `Y_doc * -1` (or ensure the `var(--y-offset)` correctly reflects this inversion for upward movement). **Adhere strictly to the Y-axis convention specified in the Design Document's section `5. 重要说明与约束` under "坐标系与CSS转换".**

**2.2. Asset Handling (HTML & CSS):**
*   **Images:** Use `<img>` tags. `src` paths **must exactly match** the `资产路径` in the Design Document.
*   **Image Preloading (JavaScript):** Implement robust image preloading (e.g., `Promise.all` for all images with `Image.onload`) before initiating the animation timeline in JavaScript.
*   **Image Display (CSS):** Use `object-fit` (e.g., `cover` or `contain`) as specified in the `CSS object-fit 建议` for each image element in the Design Document. Apply `box-shadow` or `border-radius` **only if specified**.

**2.3. Text Elements (HTML & CSS):**
*   **Content & Structure:** Text content must be exactly as in the Design Document, placed within appropriate HTML tags (e.g., `<div>` or `<span>`) identified by their `元素ID`.
*   **Styling (CSS):**
    *   `font-family`, `color`, `text-align`, `font-weight`, `font-style`, `text-shadow`, `line-height` must strictly match the Design Document's specifications for each text element.
    *   **Font Size (CSS):** Implement `font-size` using the `vw` or `px` value specified in the Design Document. If `px`, it's assumed to be for a 1920px wide canvas, so convert appropriately if responsive scaling is intended, or use directly if fixed size is intended. The document might specify `(PX_VALUE / 19.2)vw`.

## 3. Animation System & Timeline (ABSOLUTE PRECISION REQUIRED):

### 3.1 核心动画调度器 (animateScene函数):
*   **JavaScript Timeline Control:** 使用 `animateScene()` 函数作为核心调度器，通过 `setTimeout` 精确管理场景序列
*   **时间轴控制模式:**
    ```javascript
    function animateScene() {
        // 0s - 第一场景
        setTimeout(() => {
            document.getElementById('element_id').style.animation = 'fadeIn 0.5s ease-in forwards';
        }, 0);

        // 3s - 第二场景
        setTimeout(() => {
            document.getElementById('old_element').style.animation = 'fadeOut 0.3s ease-out forwards';
            document.getElementById('new_element').style.animation = 'fadeIn 0.5s ease-in forwards';
        }, 3000);
    }
    ```

### 3.2 CSS动画定义与状态管理:
*   **@keyframes 动画库:** 定义完整的动画效果集合，包括：
    *   `fadeIn`, `fadeOut` - 基础淡入淡出
    *   `slideInFromTop`, `slideInLeft`, `slideInRight` - 滑入动画
    *   `zoomToEar`, `magnifyIn` - 缩放和聚焦效果
    *   `priceScale` - 价格数字特效
    *   `fadeInUp` - 向上淡入
*   **animation-fill-mode: forwards:** 所有动画必须使用此属性确保最终状态保持
*   **CSS类切换布局:** 使用 `.final-layout` 等类实现场景布局的动态切换

### 3.3 元素层叠和替换系统:
*   **场景切换机制:**
    *   淡出当前元素: `element.style.animation = 'fadeOut 0.3s ease-out forwards'`
    *   淡入新元素: `newElement.style.animation = 'fadeIn 0.5s ease-in forwards'`
    *   时间差控制: 通过100-200ms的延迟确保平滑过渡
*   **布局类切换:**
    ```javascript
    setTimeout(() => {
        document.getElementById('container').classList.add('final-layout');
    }, 9000);
    ```

### 3.4 动态文字效果系统:
*   **打字机效果 (typewriterEffect):**
    ```javascript
    function typewriterEffect(elementId, text, duration) {
        const element = document.getElementById(elementId);
        element.style.opacity = '1';
        element.textContent = '';
        // 逐字显示逻辑
    }
    ```
*   **数字动画效果 (animatePrice):**
    ```javascript
    function animatePrice() {
        // 数字递增动画，配合缩放效果
    }
    ```

### 3.5 分镜元素管理 (CRITICAL):
*   **For EACH timeslice:** 严格按照设计文档的时间轴执行：
    *   **新增元素:** 触发入场动画并设置可见性
    *   **移除元素:** 触发退场动画后隐藏
    *   **持续显示元素:** 保持可见并应用状态变化
    *   **位置和样式更新:** 动态调整元素的尺寸、位置和样式

# CREATIVE ENHANCEMENT (Apply TASTEFULLY & ONLY where the Design Document is VAGUE or allows interpretation):

*Creative enhancements are secondary to strict document fidelity.* If the Design Document specifies an animation (e.g., "元素A: 使用CSS animation 'slideInFromLeft'"), implement that **exactly**.
If the document *only* states an element appears/disappears without detailing the transition, or if a style like `font-family` is merely "suggested" and not fixed, you may:
*   **Implement elegant default transitions:** Subtle fade-in/out (e.g., `transition: opacity 0.5s ease-in-out;`) if no specific animation is given for appearance/disappearance.
*   **Refine suggested styles:** If a font is "suggested," use it. If color is "suggested based on调性," make a sensible choice that fits the overall aesthetic described.
*   **Ensure smooth easing:** Default to common easing functions like `ease-in-out` or `cubic-bezier(0.4, 0, 0.2, 1)` for any self-implemented transitions to maintain a "cinematic feel."

**DO NOT:**
*   Introduce HTML elements (identified by `元素ID`) not listed in section 3 of the Design Document.
*   Omit any HTML element that *is* listed and should be active in a given timeslice.
*   Change specified timings, positions, dimensions, or core animation descriptions from the Design Document.
*   Over-animate; prioritize the Design Document's narrative clarity.

# AUDIO CUE COMMENTS:
In the JavaScript, at the precise time points or event triggers (e.g., start of an animation) indicated by audio cues in the Design Document's "动画与状态描述", insert clear comments:
`// AUDIO CUE: [Brief description of sound from Design Doc, e.g., "闪光音效 - 耳环旋转入场时"] - Trigger audio playback here.`

# QUALITY ASSURANCE & OUTPUT:
*   **Performance:** Aim for 60fps. Use CSS transforms and opacity for animations where possible. Minimize direct JS style manipulation in loops.
*   **Clarity & Maintainability:** Well-structured HTML, readable CSS, and heavily commented JavaScript, especially for the timeline logic and element state management within each `setTimeout`.
*   **Output:** A single, complete HTML file enclosed in a Markdown code block.

# 输出模板结构 (MANDATORY STRUCTURE):

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><!-- Title From Design Doc --></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 100vw;
            height: 100vh;
            background-color: #FFFFFF;
            font-family: '微软雅黑', 'SimHei', Arial, sans-serif;
            overflow: hidden;
            position: relative;
        }

        .container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        /* 图片元素样式 */
        .image-element {
            position: absolute;
            opacity: 0;
        }

        /* 文字元素样式 */
        .text-element {
            position: absolute;
            opacity: 0;
        }

        /* 最终场景布局类 */
        .final-layout #element_id {
            /* 布局变化样式 */
        }

        /* 动画定义 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        /* 其他必要的@keyframes动画 */
    </style>
</head>
<body>
    <div class="container" id="container">
        <!-- 所有视觉元素预定义在此 -->
        <!-- 人物图片 -->
        <!-- 道具图片 -->
        <!-- 文字元素 -->
    </div>

    <script>
        // 核心动画调度器
        function animateScene() {
            // 使用setTimeout精确控制时间轴
            // 每个场景的元素显示/隐藏/动画逻辑
        }

        // 打字机效果函数
        function typewriterEffect(elementId, text, duration) {
            // 实现逐字显示效果
        }

        // 价格动画函数
        function animatePrice() {
            // 实现数字递增动画
        }

        // 启动动画
        window.addEventListener('load', () => {
            animateScene();
        });

        // 点击重新播放
        document.addEventListener('click', () => {
            location.reload();
        });
    </script>
</body>
</html>

    """)

    # 获取响应（启用思考模式）
    response = await conversation.get_response(
        query="""
            # HTML动画设计文档 (制作蓝本)

## 1. 整体概念与目标
*   **动画总时长：** 12秒
*   **动画核心内容/叙事：** 展示黄杨钿甜在成人礼上佩戴的230万元格拉夫耳环引发的争议，重点突出"奢侈品与公务员身份不符"这一核心争议点。
*   **动画风格与目的（若聊天记录明确）：** 信息图表式结合新闻调查风格，清晰呈现事件关键点，保持严肃专业调性

## 2. 画布尺寸与输出格式
*   **画布（容器）目标尺寸：** 1920×1080像素 (宽高比 16:9)。HTML实现时，通常使用 `100vw` 和 `100vh` 并确保内部元素按此比例缩放。
*   **最终输出格式：** HTML动画页面 (用于浏览器播放或录制成视频)

## 3. 主要组成部分 (基于导演最终采纳方案)

### 3.1 人物元素列表 (HTML `<img>` 标签)
*   **元素ID：** char_hydt
    *   **人物名称/代号：** 黄杨钿甜
    *   **描述：** 事件的核心人物，童星，因在成人礼佩戴高价耳环引发对其家庭背景和财富来源的广泛争议。
    *   **资产路径 (src属性)：** 黄杨钿甜.jpg
    *   **宽高比 (原始图片)：** 0.75
    *   **CSS `object-fit` 建议：** cover

*   **元素ID：** char_hydt_closeup
    *   **人物名称/代号：** 黄杨钿甜耳环佩戴局部特写
    *   **描述：** 黄杨钿甜佩戴耳环的侧脸特写照片，清晰展示耳环在其耳朵上的实际佩戴效果
    *   **资产路径 (src属性)：** 黄杨钿甜耳环佩戴局部特写.jpg
    *   **宽高比 (原始图片)：** 1.00
    *   **CSS `object-fit` 建议：** contain

### 3.2 道具元素列表 (HTML `<img>` 标签)
*   **元素ID：** prop_earring
    *   **道具名称/代号：** 黄杨钿甜佩戴的GRAFF耳环
    *   **描述：** 黄杨钿甜成人礼上佩戴的格拉夫（GRAFF）祖母绿钻石耳环，公价传闻约230万元，是引发整个事件的导火索。
    *   **资产路径 (src属性)：** 黄杨钿甜佩戴的graff耳环.jpg
    *   **宽高比 (原始图片)：** 1.00
    *   **CSS `object-fit` 建议：** contain

*   **元素ID：** prop_income_chart
    *   **道具名称/代号：** 公务员收入数据图表
    *   **描述：** 展示一般公务员家庭年收入数据的简洁图表或信息图，作为与230万元耳环价格的对比参考
    *   **资产路径 (src属性)：** 公务员收入数据图表.png
    *   **宽高比 (原始图片)：** 1.78
    *   **CSS `object-fit` 建议：** contain

### 3.3 背景设计 (HTML `<body>` 或容器 `<div>` 样式)
*   **背景类型：** 纯色
*   **CSS样式/资产路径：** #FFFFFF
*   **动画效果（若聊天记录明确且被导演采纳）：** 静态背景

### 3.4 文字关键词元素 (HTML `<div>`或`<span>` 标签)
*   **元素ID：** text_title
    *   **文字内容：** "奢侈耳环"
    *   **出现时间范围 (全局)：** 0s - 12s
    *   **位置 (X,Y 单位，基于14x8网格中心点0,0，用于转换为CSS定位)：** 0, 3.5
        *   **CSS 定位建议：** position: absolute; left: 50vw; top: calc(50vh - 3.5 * (100vh/8)); transform: translate(-50%, -50%);
    *   **字体 (CSS `font-family`)：** '微软雅黑', 'SimHei', Arial, sans-serif
    *   **字号 (CSS `font-size`)：** 24px (约1.25vw)
    *   **颜色 (CSS `color` HEX)：** #00008B (深蓝色)
    *   **对齐方式 (CSS `text-align`，若明确)：** center
    *   **其他CSS样式建议：** font-weight: bold;

*   **元素ID：** text_tag
    *   **文字内容：** "争议事件"
    *   **出现时间范围 (全局)：** 0s - 12s
    *   **位置 (X,Y 单位，基于14x8网格中心点0,0，用于转换为CSS定位)：** 6, 3.5
        *   **CSS 定位建议：** position: absolute; left: calc(50vw + 6 * (100vw/14)); top: calc(50vh - 3.5 * (100vh/8)); transform: translate(-50%, -50%);
    *   **字体 (CSS `font-family`)：** '微软雅黑', 'SimHei', Arial, sans-serif
    *   **字号 (CSS `font-size`)：** 16px (约0.85vw)
    *   **颜色 (CSS `color` HEX)：** #FFFFFF (白色背景为#FF0000红色)
    *   **对齐方式 (CSS `text-align`，若明确)：** center
    *   **其他CSS样式建议：** font-weight: normal; background-color: #FF0000; padding: 3px 8px; border-radius: 4px;

*   **元素ID：** text_date
    *   **文字内容：** "2025年5月"
    *   **出现时间范围 (全局)：** 0s - 3s
    *   **位置 (X,Y 单位，基于14x8网格中心点0,0，用于转换为CSS定位)：** -5, -3
        *   **CSS 定位建议：** position: absolute; left: calc(50vw - 5 * (100vw/14)); top: calc(50vh + 3 * (100vh/8)); transform: translate(-50%, -50%);
    *   **字体 (CSS `font-family`)：** '微软雅黑', 'SimHei', Arial, sans-serif
    *   **字号 (CSS `font-size`)：** 18px (约0.95vw)
    *   **颜色 (CSS `color` HEX)：** #000000 (黑色)
    *   **对齐方式 (CSS `text-align`，若明确)：** left
    *   **其他CSS样式建议：** font-weight: normal;

*   **元素ID：** text_brand
    *   **文字内容：** "GRAFF奢侈珠宝"
    *   **出现时间范围 (全局)：** 3s - 6s
    *   **位置 (X,Y 单位，基于14x8网格中心点0,0，用于转换为CSS定位)：** 0, -1.5
        *   **CSS 定位建议：** position: absolute; left: 50vw; top: calc(50vh + 1.5 * (100vh/8)); transform: translate(-50%, -50%);
    *   **字体 (CSS `font-family`)：** '微软雅黑', 'SimHei', Arial, sans-serif
    *   **字号 (CSS `font-size`)：** 20px (约1.05vw)
    *   **颜色 (CSS `color` HEX)：** #00008B (深蓝色)
    *   **对齐方式 (CSS `text-align`，若明确)：** center
    *   **其他CSS样式建议：** font-weight: bold;

*   **元素ID：** text_price
    *   **文字内容：** "¥2,300,000"
    *   **出现时间范围 (全局)：** 6s - 9s
    *   **位置 (X,Y 单位，基于14x8网格中心点0,0，用于转换为CSS定位)：** 0, -1.5
        *   **CSS 定位建议：** position: absolute; left: 50vw; top: calc(50vh + 1.5 * (100vh/8)); transform: translate(-50%, -50%);
    *   **字体 (CSS `font-family`)：** '微软雅黑', 'SimHei', Arial, sans-serif
    *   **字号 (CSS `font-size`)：** 28px (约1.45vw)
    *   **颜色 (CSS `color` HEX)：** #FF0000 (红色)
    *   **对齐方式 (CSS `text-align`，若明确)：** center
    *   **其他CSS样式建议：** font-weight: bold;

*   **元素ID：** text_comparison
    *   **文字内容：** "公务员收入 vs 耳环价值"
    *   **出现时间范围 (全局)：** 9s - 12s
    *   **位置 (X,Y 单位，基于14x8网格中心点0,0，用于转换为CSS定位)：** 0, 0.5
        *   **CSS 定位建议：** position: absolute; left: 50vw; top: calc(50vh - 0.5 * (100vh/8)); transform: translate(-50%, -50%);
    *   **字体 (CSS `font-family`)：** '微软雅黑', 'SimHei', Arial, sans-serif
    *   **字号 (CSS `font-size`)：** 18px (约0.95vw)
    *   **颜色 (CSS `color` HEX)：** #000000 (黑色)
    *   **对齐方式 (CSS `text-align`，若明确)：** center
    *   **其他CSS样式建议：** font-weight: bold;

*   **元素ID：** text_conclusion
    *   **文字内容：** "经济条件严重不符"
    *   **出现时间范围 (全局)：** 9s - 12s
    *   **位置 (X,Y 单位，基于14x8网格中心点0,0，用于转换为CSS定位)：** 0, -3.5
        *   **CSS 定位建议：** position: absolute; left: 50vw; top: calc(50vh + 3.5 * (100vh/8)); transform: translate(-50%, -50%);
    *   **字体 (CSS `font-family`)：** '微软雅黑', 'SimHei', Arial, sans-serif
    *   **字号 (CSS `font-size`)：** 22px (约1.15vw)
    *   **颜色 (CSS `color` HEX)：** #8B0000 (深红色)
    *   **对齐方式 (CSS `text-align`，若明确)：** center
    *   **其他CSS样式建议：** font-weight: bold;

## 4. 分镜/场景与音频同步列表 (HTML动画时间轴依据)

*   **时间戳：** 0s ~ 3s
    *   **音频/独白内容 (此段)：** "二零二五年五月，黄杨钿甜在成人礼上佩戴的格拉夫祖母绿钻石耳环照片在网上传播，"
    *   **元素状态变化 (此时间戳内)：**
        *   **新增元素 (IDs)：** char_hydt, text_title, text_tag, text_date
        *   **移除元素 (IDs)：** 无
        *   **持续显示元素 (IDs)：** 无
    *   **视觉构成与布局 (针对此时间戳内所有*活跃*元素，包括新增和持续显示的)：**
        1.  **元素ID：** char_hydt
            *   **类型：** 图片
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** 32.14vw, 42.85vh
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: 50vw; top: 50vh; transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** opacity: 1;
        2.  **元素ID：** text_title
            *   **类型：** 文字
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** auto, auto
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: 50vw; top: calc(50vh - 3.5 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** color: #00008B; font-weight: bold; font-size: 24px;
        3.  **元素ID：** text_tag
            *   **类型：** 文字
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** auto, auto
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: calc(50vw + 6 * (100vw/14)); top: calc(50vh - 3.5 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** color: #FFFFFF; background-color: #FF0000; padding: 3px 8px; border-radius: 4px; font-size: 16px;
        4.  **元素ID：** text_date
            *   **类型：** 文字
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** auto, auto
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: calc(50vw - 5 * (100vw/14)); top: calc(50vh + 3 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** color: #000000; font-size: 18px;
    *   **动画与状态描述 (CSS Animations/Transitions 或 JavaScript控制逻辑)：**
        *   新增元素 [char_hydt]: 使用CSS animation 'fadeIn' (定义：从opacity:0到opacity:1)，duration: 0.5s, easing: ease-in, fill-mode: forwards。在0s开始。
        *   新增元素 [text_title]: 使用CSS animation 'slideInFromTop' (定义：从transform: translateY(-50px) opacity:0到transform: translateY(0) opacity:1)，duration: 0.7s, easing: ease-out, fill-mode: forwards。在0.2s开始。
        *   新增元素 [text_tag]: 使用CSS animation 'fadeIn' (定义：从opacity:0到opacity:1)，duration: 0.5s, easing: ease-in, fill-mode: forwards。在0.5s开始。
        *   新增元素 [text_date]: 使用CSS animation 'fadeIn' (定义：从opacity:0到opacity:1)，duration: 0.5s, easing: ease-in, fill-mode: forwards。在0.7s开始。
        *   持续元素 [char_hydt]: 在2.5s开始使用CSS animation 'zoomToEar' (定义：从transform: scale(1) translate(-50%, -50%)到transform: scale(1.5) translate(-60%, -40%))，duration: 1s, easing: ease-in-out, fill-mode: forwards，逐渐放大并移动到耳朵位置，为下一帧过渡到耳环特写做准备。

*   **时间戳：** 3s ~ 6s
    *   **音频/独白内容 (此段)：** "引发公众对其家庭财富来源的质疑，这对耳环市场价值约"
    *   **元素状态变化 (此时间戳内)：**
        *   **新增元素 (IDs)：** char_hydt_closeup, text_brand
        *   **移除元素 (IDs)：** char_hydt, text_date
        *   **持续显示元素 (IDs)：** text_title, text_tag
    *   **视觉构成与布局 (针对此时间戳内所有*活跃*元素，包括新增和持续显示的)：**
        1.  **元素ID：** char_hydt_closeup
            *   **类型：** 图片
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** 50vw, 50vh
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: 50vw; top: 50vh; transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** opacity: 1;
        2.  **元素ID：** text_title
            *   **类型：** 文字
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** auto, auto
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: 50vw; top: calc(50vh - 3.5 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** color: #00008B; font-weight: bold; font-size: 24px;
        3.  **元素ID：** text_tag
            *   **类型：** 文字
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** auto, auto
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: calc(50vw + 6 * (100vw/14)); top: calc(50vh - 3.5 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** color: #FFFFFF; background-color: #FF0000; padding: 3px 8px; border-radius: 4px; font-size: 16px;
        4.  **元素ID：** text_brand
            *   **类型：** 文字
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** auto, auto
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: 50vw; top: calc(50vh + 1.5 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** color: #00008B; font-weight: bold; font-size: 20px; opacity: 1;
    *   **动画与状态描述 (CSS Animations/Transitions 或 JavaScript控制逻辑)：**
        *   移除元素 [char_hydt]: 使用CSS animation 'fadeOut' (定义：从opacity:1到opacity:0)，duration: 0.3s, easing: ease-out, fill-mode: forwards。在3s开始。
        *   移除元素 [text_date]: 使用CSS animation 'fadeOut' (定义：从opacity:1到opacity:0)，duration: 0.3s, easing: ease-out, fill-mode: forwards。在3s开始。
        *   新增元素 [char_hydt_closeup]: 使用CSS animation 'magnifyIn' (定义：从transform: scale(0.8) translate(-50%, -50%) opacity: 0到transform: scale(1) translate(-50%, -50%) opacity: 1)，duration: 0.5s, easing: ease-out, fill-mode: forwards，配合放大镜音效。在3.1s开始。
        *   持续元素 [text_title, text_tag]: 保持静态，不变。
        *   新增元素 [text_brand]: 使用CSS animation 'typewriterEffect' (JavaScript控制的打字机效果，从无到逐字显示)，duration: 0.8s。在3.8s开始。

*   **时间戳：** 6s ~ 9s
    *   **音频/独白内容 (此段)：** "二百三十万元，与一般"
    *   **元素状态变化 (此时间戳内)：**
        *   **新增元素 (IDs)：** prop_earring, text_price
        *   **移除元素 (IDs)：** char_hydt_closeup, text_brand
        *   **持续显示元素 (IDs)：** text_title, text_tag
    *   **视觉构成与布局 (针对此时间戳内所有*活跃*元素，包括新增和持续显示的)：**
        1.  **元素ID：** prop_earring
            *   **类型：** 图片
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** 42.85vw, 42.85vh
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: 50vw; top: 50vh; transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** opacity: 1;
        2.  **元素ID：** text_title
            *   **类型：** 文字
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** auto, auto
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: 50vw; top: calc(50vh - 3.5 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** color: #00008B; font-weight: bold; font-size: 24px;
        3.  **元素ID：** text_tag
            *   **类型：** 文字
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** auto, auto
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: calc(50vw + 6 * (100vw/14)); top: calc(50vh - 3.5 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** color: #FFFFFF; background-color: #FF0000; padding: 3px 8px; border-radius: 4px; font-size: 16px;
        4.  **元素ID：** text_price
            *   **类型：** 文字
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** auto, auto
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: 50vw; top: calc(50vh + 1.5 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** color: #FF0000; font-weight: bold; font-size: 28px; opacity: 1;
    *   **动画与状态描述 (CSS Animations/Transitions 或 JavaScript控制逻辑)：**
        *   移除元素 [char_hydt_closeup]: 使用CSS animation 'fadeOut' (定义：从opacity:1到opacity:0)，duration: 0.3s, easing: ease-out, fill-mode: forwards。在6s开始。
        *   移除元素 [text_brand]: 使用CSS animation 'fadeOut' (定义：从opacity:1到opacity:0)，duration: 0.3s, easing: ease-out, fill-mode: forwards。在6s开始。
        *   新增元素 [prop_earring]: 使用CSS animation 'fadeIn' (定义：从opacity:0到opacity:1)，duration: 0.5s, easing: ease-in, fill-mode: forwards。在6.1s开始。
        *   持续元素 [text_title, text_tag]: 保持静态，不变。
        *   新增元素 [text_price]: 使用JavaScript控制，数字从0快速跳转到2,300,000的动画效果，配合计数器声音。动画持续时间控制在1秒以内，在6.3s开始，6.8s完成。效果定义为从 "¥0" 到 "¥2,300,000" 的数字计数动画，同时从小变大 (transform: scale(0.5) 到 scale(1))。

*   **时间戳：** 9s ~ 12s
    *   **音频/独白内容 (此段)：** "公务员家庭经济条件严重不符。"
    *   **元素状态变化 (此时间戳内)：**
        *   **新增元素 (IDs)：** char_hydt, prop_income_chart, text_comparison, text_conclusion
        *   **移除元素 (IDs)：** prop_earring, text_price
        *   **持续显示元素 (IDs)：** text_title, text_tag
    *   **视觉构成与布局 (针对此时间戳内所有*活跃*元素，包括新增和持续显示的)：**
        1.  **元素ID：** char_hydt
            *   **类型：** 图片
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** 21.43vw, 28.57vh
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: calc(50vw - 3 * (100vw/14)); top: calc(50vh - 2 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** opacity: 1;
        2.  **元素ID：** prop_earring
            *   **类型：** 图片
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** 21.43vw, 21.43vh
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: calc(50vw + 3 * (100vw/14)); top: calc(50vh - 2 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** opacity: 1;
        3.  **元素ID：** prop_income_chart
            *   **类型：** 图片
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** 57.14vw, 32.14vh
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: 50vw; top: calc(50vh + 1.25 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** opacity: 1;
        4.  **元素ID：** text_title
            *   **类型：** 文字
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** auto, auto
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: 50vw; top: calc(50vh - 3.5 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** color: #00008B; font-weight: bold; font-size: 24px;
        5.  **元素ID：** text_tag
            *   **类型：** 文字
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** auto, auto
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: calc(50vw + 6 * (100vw/14)); top: calc(50vh - 3.5 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** color: #FFFFFF; background-color: #FF0000; padding: 3px 8px; border-radius: 4px; font-size: 16px;
        6.  **元素ID：** text_comparison
            *   **类型：** 文字
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** auto, auto
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: 50vw; top: calc(50vh - 0.5 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** color: #000000; font-weight: bold; font-size: 18px; opacity: 1;
        7.  **元素ID：** text_conclusion
            *   **类型：** 文字
            *   **当前尺寸 (CSS `width`/`height`，基于网格单位计算的vw/vh)：** auto, auto
            *   **当前位置 (CSS `left`/`top`/`transform`，基于网格坐标计算)：** position: absolute; left: 50vw; top: calc(50vh + 3.5 * (100vh/8)); transform: translate(-50%, -50%);
            *   **当前主要CSS样式：** color: #8B0000; font-weight: bold; font-size: 22px; opacity: 1;
    *   **动画与状态描述 (CSS Animations/Transitions 或 JavaScript控制逻辑)：**
        *   移除元素 [prop_earring]: 使用CSS animation 'slideOutLeft' (定义：从当前位置到transform: translateX(-100vw))，duration: 0.5s, easing: ease-in, fill-mode: forwards。在9s开始。
        *   移除元素 [text_price]: 使用CSS animation 'fadeOut' (定义：从opacity:1到opacity:0)，duration: 0.3s, easing: ease-out, fill-mode: forwards。在9s开始。
        *   新增元素 [char_hydt]: 使用CSS animation 'slideInLeft' (定义：从transform: translateX(-100vw) opacity:0 到 当前位置 opacity:1)，duration: 0.5s, easing: ease-out, fill-mode: forwards。在9.1s开始。
        *   新增元素 [prop_earring]: 使用CSS animation 'slideInRight' (定义：从transform: translateX(100vw) opacity:0 到 当前位置 opacity:1)，duration: 0.5s, easing: ease-out, fill-mode: forwards。在9.1s开始。
        *   新增元素 [prop_income_chart]: 使用CSS animation 'fadeInUp' (定义：从transform: translateY(50px) opacity:0 到 当前位置 opacity:1)，duration: 0.5s, delay: 0.3s, easing: ease-out, fill-mode: forwards，配合笔记本翻页声。在9.2s开始。
        *   持续元素 [text_title, text_tag]: 保持静态，不变。
        *   新增元素 [text_comparison]: 使用CSS animation 'fadeIn' (定义：从opacity:0到opacity:1)，duration: 0.4s, delay: 0.6s, easing: ease-in, fill-mode: forwards。在9.4s开始。
        *   新增元素 [text_conclusion]: 使用CSS animation 'typewriterEffect' (JavaScript控制的打字机效果，从无到逐字显示)，duration: 1s, delay: 0.8s。在9.8s开始，并在10.8s完成。最后1.2秒保持静态画面，给观众留下深刻印象。

## 5. 重要说明与约束
*   **素材引用：** 所有图片素材的`src`路径必须与导演最终确认的列表一致。
*   **坐标系与CSS转换：**
    *   逻辑画布为14个宽度单位 × 8个高度单位，原点(0,0)为中心。
    *   X轴：-7 (最左) 到 +7 (最右)。 Y轴：-4 (最下) 到 +4 (最上)。
    *   此逻辑坐标和尺寸需转换为CSS单位（`vw`, `vh`, `%`结合`transform`）。例如，1单位宽度 ≈ `(100/14)vw`，1单位高度 ≈ `(100/8)vh`。元素中心点 `(X_unit, Y_unit)` 可通过 `left: calc(50% + X_unit * (100/14)vw); top: calc(50% - Y_unit * (100/8)vh); transform: translate(-50%, -50%);` （注意Y轴方向，若Y正向上则用减号，若Y正向下则用加号）或类似方式实现。
*   **响应式设计：** 所有尺寸和定位应优先使用相对单位（`vw`, `vh`, `%`）以确保在不同屏幕上的16:9比例内正确显示。
*   **元素不重叠：** 除非聊天记录明确指示或设计需要，否则应避免元素在视觉上不期望地重叠。若有重叠，需通过`z-index`明确层级关系。
*   **动画实现：** 优先考虑使用CSS Animations 和 Transitions 以获得更佳性能。复杂的同步或交互逻辑可由JavaScript (`setTimeout`, `requestAnimationFrame`) 控制。
*   **浏览器兼容性：** 设计应考虑主流现代浏览器（Chrome, Firefox, Safari, Edge）。

""",
        enable_thinking=True,
        thinking_budget_tokens=2000
    )
    print(f"响应: {response}")

if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())